<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archive Copy Tool - Professional File Manager</title>
    <script src="../static/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="../static/css/explorer.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    {% include 'topbar.html' %}

    <div class="main-container">
        <div class="header-section">
            <div class="app-title">
                <i class="fas fa-archive"></i>
                <h1>Archive Copy Tool</h1>
                <span class="subtitle">Professional File Management System</span>
            </div>
        </div>

        <div class="explorer-container">
            <form id="copyForm">
                <!-- Source Explorer Panel -->
                <div class="explorer-panel">
                    <div class="panel-header">
                        <div class="panel-title">
                            <i class="fas fa-folder-open"></i>
                            <span>Source Directory</span>
                        </div>
                        <div class="panel-controls">
                            <button type="button" class="control-btn" onclick="refreshDirectory('src')" title="Refresh">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button type="button" class="control-btn" onclick="goHome('src')" title="Home">
                                <i class="fas fa-home"></i>
                            </button>
                        </div>
                    </div>

                    <div class="address-bar">
                        <div class="breadcrumb-container">
                            <i class="fas fa-map-marker-alt"></i>
                            <span id="breadcrumb-src" class="breadcrumb"></span>
                        </div>
                        <input type="text" id="src" name="src" hidden>
                    </div>

                    <div class="explorer-content" id="src-container">
                        <div class="directory-grid" id="src-list"></div>
                        <div class="loading-indicator" id="src-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading directories...</span>
                        </div>
                    </div>
                </div>

                <!-- Destination Explorer Panel -->
                <div class="explorer-panel">
                    <div class="panel-header">
                        <div class="panel-title">
                            <i class="fas fa-folder"></i>
                            <span>Destination Directory</span>
                        </div>
                        <div class="panel-controls">
                            <button type="button" class="control-btn" onclick="refreshDirectory('dst')" title="Refresh">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button type="button" class="control-btn" onclick="goHome('dst')" title="Home">
                                <i class="fas fa-home"></i>
                            </button>
                        </div>
                    </div>

                    <div class="address-bar">
                        <div class="breadcrumb-container">
                            <i class="fas fa-map-marker-alt"></i>
                            <span id="breadcrumb-dst" class="breadcrumb"></span>
                        </div>
                        <input type="text" id="dst" name="dst" hidden>
                    </div>

                    <div class="explorer-content" id="dst-container">
                        <div class="directory-grid" id="dst-list"></div>
                        <div class="loading-indicator" id="dst-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading directories...</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Action Panel -->
        <div class="action-panel">
            <div class="copy-controls">
                <button type="submit" id="startButton" class="action-btn primary" form="copyForm">
                    <i class="fas fa-copy"></i>
                    <span>Start Copy Operation</span>
                </button>
                <button type="button" id="stopButton" class="action-btn danger" style="display:none;">
                    <i class="fas fa-stop"></i>
                    <span>Stop Copy Operation</span>
                </button>
            </div>

            <div id="progress-container" class="progress-section" style="display: none;">
                <div class="progress-header">
                    <span class="progress-title">Copy Progress</span>
                    <span class="progress-percentage" id="progress-percentage">0%</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" id="progress-bar">
                        <div class="progress-bar-fill" id="progress-bar-fill">
                            <span class="progress-text">0%</span>
                        </div>
                    </div>
                </div>
                <div class="progress-info">
                    <span id="progress-status">Preparing to copy...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Check the current status of the copy process
            $.get('/progress', function(data) {
                if (data.copying) {
                    $('#progress-container').show();
                    $('#startButton').hide();
                    $('#stopButton').show();
                    updateProgress();
                }
            });

            $('#copyForm').on('submit', function(e) {
                e.preventDefault();

                // Validate selection
                if (!$('#src').val() || !$('#dst').val()) {
                    showNotification('Please select both source and destination directories', 'error');
                    return;
                }

                $('#progress-container').show();
                $('#startButton').hide();
                $('#stopButton').show();
                $('#progress-status').text('Initializing copy operation...');

                var formData = new FormData(this);
                $.ajax({
                    url: '/copy',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(data) {
                        if (data.status === 'copying started') {
                            $('#progress-status').text('Copy operation in progress...');
                            updateProgress();
                            showNotification('Copy operation started successfully', 'success');
                        }
                    },
                    error: function() {
                        showNotification('Failed to start copy operation', 'error');
                        $('#startButton').show();
                        $('#stopButton').hide();
                        $('#progress-container').hide();
                    }
                });
            });

            $('#stopButton').on('click', function() {
                if (confirm("Are you sure you want to stop the copy operation?")) {
                    $.post('/stop', function(data) {
                        if (data.status === 'copying stopped') {
                            $('#startButton').show();
                            $('#stopButton').hide();
                            $('#progress-container').hide();
                            $('#progress-status').text('Copy operation stopped');
                            showNotification('Copy operation stopped', 'warning');
                        }
                    });
                }
            });

            function updateProgress() {
                $.get('/progress', function(data) {
                    const percentage = data.progress.toFixed(1);
                    $('#progress-bar-fill').css('width', percentage + '%');
                    $('.progress-text').text(percentage + '%');
                    $('#progress-percentage').text(percentage + '%');

                    if (data.progress < 100 && data.copying) {
                        $('#progress-status').text('Copying files... ' + percentage + '% complete');
                        setTimeout(updateProgress, 1000);
                    } else {
                        $('#startButton').show();
                        $('#stopButton').hide();
                        if (data.progress >= 100) {
                            $('#progress-status').text('Copy operation completed successfully!');
                            showNotification('Copy operation completed successfully!', 'success');
                        }
                    }
                });
            }

            // Initialize directories
            selectDirectory('src');
            selectDirectory('dst');
            chooseDirectory(currentDirectory['src'], 'src');
            chooseDirectory(currentDirectory['dst'], 'dst');
        });

        // Default directories - adjust these paths as needed
        let DEFAULT_SRC = '/';
        let DEFAULT_DST = '/';

        let currentDirectory = {
            src: DEFAULT_SRC,
            dst: DEFAULT_DST
        };

        function selectDirectory(type) {
            showLoading(type, true);
            listDirectories(currentDirectory[type], type);
        }

        function listDirectories(directory, type) {
            showLoading(type, true);

            $.post('/list_directories', { directory: directory }, function(data) {
                showLoading(type, false);

                if (data.error) {
                    currentDirectory[type] = '/';
                    listDirectories(currentDirectory[type], type);
                    showNotification('Directory not found, redirected to root', 'warning');
                    return;
                }

                const list = (type === 'src') ? $('#src-list') : $('#dst-list');
                list.empty();

                // Add "Go Up" button if not at root
                if (directory !== "/") {
                    let arr = directory.split('/').slice(0, -1);
                    let parentDirectory = arr.length > 1 ? arr.join('/') : '/';

                    list.append(`
                        <div class="directory-item go-up" onclick="chooseDirectory('${parentDirectory}', '${type}')">
                            <div class="item-icon">
                                <i class="fas fa-level-up-alt"></i>
                            </div>
                            <div class="item-info">
                                <div class="item-name">.. (Go Up)</div>
                                <div class="item-details">Parent Directory</div>
                            </div>
                        </div>
                    `);
                }

                // Add directories
                data.subdirectories.forEach(function(subdir) {
                    let childDirectory = directory !== '/' ? directory + '/' + subdir : '/' + subdir;
                    let displayName = subdir.length > 25 ? subdir.substring(0, 25) + '...' : subdir;

                    list.append(`
                        <div class="directory-item" onclick="chooseDirectory('${childDirectory}', '${type}')" title="${subdir}">
                            <div class="item-icon">
                                <i class="fas fa-folder"></i>
                            </div>
                            <div class="item-info">
                                <div class="item-name">${displayName}</div>
                                <div class="item-details">Folder</div>
                            </div>
                        </div>
                    `);
                });

                if (data.subdirectories.length === 0) {
                    list.append(`
                        <div class="empty-directory">
                            <i class="fas fa-folder-open"></i>
                            <span>This directory is empty</span>
                        </div>
                    `);
                }
            }).fail(function() {
                showLoading(type, false);
                showNotification('Failed to load directory contents', 'error');
            });
        }

        function chooseDirectory(directory, type) {
            currentDirectory[type] = directory;
            const input = (type === 'src') ? $('#src') : $('#dst');
            input.val(directory);

            // Update breadcrumb
            updateBreadcrumb(directory, type);

            // Load directory contents
            listDirectories(directory, type);
        }

        function updateBreadcrumb(directory, type) {
            const breadcrumb = (type === 'src') ? $('#breadcrumb-src') : $('#breadcrumb-dst');
            breadcrumb.text(directory);
        }

        function showLoading(type, show) {
            const loading = (type === 'src') ? $('#src-loading') : $('#dst-loading');
            if (show) {
                loading.show();
            } else {
                loading.hide();
            }
        }

        function refreshDirectory(type) {
            listDirectories(currentDirectory[type], type);
            showNotification('Directory refreshed', 'info');
        }

        function goHome(type) {
            const homeDir = (type === 'src') ? DEFAULT_SRC : DEFAULT_DST;
            chooseDirectory(homeDir, type);
            showNotification('Navigated to home directory', 'info');
        }

        function showNotification(message, type = 'info') {
            // Remove existing notifications
            $('.notification').remove();

            const iconMap = {
                'success': 'fa-check-circle',
                'error': 'fa-exclamation-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            };

            const notification = $(`
                <div class="notification notification-${type}">
                    <i class="fas ${iconMap[type]}"></i>
                    <span>${message}</span>
                    <button class="notification-close" onclick="$(this).parent().remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `);

            $('body').append(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                notification.fadeOut(() => notification.remove());
            }, 5000);
        }

        function cleanUp(clean_up_destination) {
            if (confirm("⚠️ WARNING: This will permanently delete all files in:\n\n" + clean_up_destination + "\n\nThis action cannot be undone. Are you sure you want to continue?")) {
                showNotification('Starting cleanup operation...', 'info');
                $.post('/clean-up', { clean_up_destination: clean_up_destination }, function(data) {
                    if (data.status === 'successful') {
                        showNotification('Cleanup completed successfully for ' + data.destination, 'success');
                    } else {
                        showNotification('Cleanup failed for ' + data.destination + ': ' + data.status, 'error');
                    }
                }).fail(function() {
                    showNotification('Cleanup operation failed due to server error', 'error');
                });
            }
        }

        function sizeReport(destination) {
            showNotification('Generating size report for ' + destination + '...', 'info');
            $.post('/size-report', { destination: destination }, function(data) {
                if (data.download_url) {
                    showNotification('Size report generated successfully', 'success');
                    window.location.href = data.download_url;
                } else {
                    showNotification('Failed to generate the size report', 'error');
                }
            }).fail(function() {
                showNotification('An error occurred while generating the size report', 'error');
            });
        }

    </script>
</body>
</html>
