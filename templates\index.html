<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Archives Copy</title>
    <script src="../static/jquery-3.6.0.min.js"></script>
    <link rel="stylesheet" href="../static/css/styles.css">
</head>
<body>
    {% include 'topbar.html' %}
    <h1>Archives Copy</h1>
    <div class="container">
        <form id="copyForm">
            <h2>Source Directory</h2>
            <label id="label_src"></label>
            <input type="text" id="src" name="src" hidden style="width: 100%;"><br>
            <div id="src-container">
                <!-- <button type="button" onclick="selectDirectory('src')">Select Source Directory</button> -->
                <ul id="src-list" class="directory-list"></ul>
            </div>
            
            <h2>Destination Directory</h2>
            <label id="label_dst"></label>
            <input type="text" id="dst" name="dst" hidden style="width: 100%"><br>
            <div id="dst-container">
                <!-- <button type="button" onclick="selectDirectory('dst')">Select Destination Directory</button> -->
                <ul id="dst-list" class="directory-list"></ul>
            </div>
            
            <button type="submit" id="startButton" class="copy-btn">Start Copy</button>
            <button type="button" id="stopButton" class="copy-btn" style="display:none;background-color:orangered">Stop Copy</button>
        </form>
        <div id="progress-container" style="display: none;">
            <div id="progress-bar">
                <div id="progress-bar-fill">0%</div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Check the current status of the copy process
            $.get('/progress', function(data) {
                if (data.copying) {
                    $('#progress-container').show();
                    $('#startButton').hide();
                    $('#stopButton').show();
                    updateProgress();
                }
            });

            $('#copyForm').on('submit', function(e) {
                e.preventDefault();
                $('#progress-container').show();
                $('#startButton').hide();
                $('#stopButton').show();

                var formData = new FormData(this);
                $.ajax({
                    url: '/copy',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(data) {
                        if (data.status === 'copying started') {
                            updateProgress();
                        }
                    }
                });
            });

            $('#stopButton').on('click', function() {
                if (confirm("Are you sure you want to stop the copy operation?")) {
                    $.post('/stop', function(data) {
                        if (data.status === 'copying stopped') {
                            $('#startButton').show();
                            $('#stopButton').hide();
                        }
                    });
                }
            });

            function updateProgress() {
                $.get('/progress', function(data) {
                    $('#progress-bar-fill').css('width', data.progress + '%');
                    $('#progress-bar-fill').text(data.progress.toFixed(2) + '%');
                    $('#progress-percentage').text(data.progress.toFixed(2) + '%');
                    if (data.progress < 100 && data.copying) {
                        setTimeout(updateProgress, 1000);
                    } else {
                        $('#startButton').show();
                        $('#stopButton').hide();
                    }
                });
            }

            selectDirectory('src');
            selectDirectory('dst');
            chooseDirectory(currentDirectory['src'], 'src');
            chooseDirectory(currentDirectory['dst'], 'dst');
        });

        // let DEFAULT_SRC = '/Users/<USER>/Downloads';
        // let DEFAULT_DST = '/Users/<USER>/Desktop';
        let DEFAULT_SRC = '/vnas1/nandi/video/00_trimbkup';
        let DEFAULT_DST = '/data/dwara/workspace/edited/user/venugopal.reddy/transfer/Check';
        

        let currentDirectory = {
            src: DEFAULT_SRC,  // Adjust to your server's base directory
            dst: DEFAULT_DST   // Adjust to your server's base directory
        };

        function selectDirectory(type) {
            listDirectories(currentDirectory[type], type);
        }

        function listDirectories(directory, type) {
            $.post('/list_directories', { directory: directory }, function(data) {
                // console.log("error " + data.error);
                if (data.error) {
                    // alert(data.error);
                    currentDirectory[type] = '/';
                    listDirectories(currentDirectory[type], type);
                    if(type == 'src') {
                        document.getElementById('label_src').textContent = currentDirectory[type];
                    }
                    else {
                        document.getElementById('label_dst').textContent = currentDirectory[type];
                    }
                    return;
                }
                const list = (type === 'src') ? $('#src-list') : $('#dst-list');
                list.empty();
                // console.log("test " + directory);
                if (directory !== "/") {  // Adjust to your server's base directory
                    let arr = directory.split('/').slice(0, -1);
                    if (arr.length > 1)
                        parentDirectory = arr.join('/');
                    else
                        parentDirectory = '/';
                    list.append(`<li class="directory-item" onclick="chooseDirectory('${parentDirectory}', '${type}')">.. (Go Up)</li>`);
                }
                data.subdirectories.forEach(function(subdir) {
                    if(directory !== '/')
                        childDirectory = directory + '/' + subdir;
                    else
                        childDirectory = '/' + subdir;
                    list.append(`<li class="directory-item" onclick="chooseDirectory('${childDirectory}', '${type}')">${subdir}</li>`);
                });
            });
        }

        function chooseDirectory(directory, type) {
            currentDirectory[type] = directory;
            const input = (type === 'src') ? $('#src') : $('#dst');
            input.val(directory);
            if(type == 'src') {
                document.getElementById('label_src').textContent = directory;
            }
            else {
                document.getElementById('label_dst').textContent = directory;
            }
            listDirectories(directory, type);
        }

        function cleanUp(clean_up_destination) {
            // Your cleanup code here
            if (confirm("Are you sure you want to clean up " + clean_up_destination + " ?")) {
                    $.post('/clean-up', { clean_up_destination: clean_up_destination }, function(data) {
                        alert('Clean up ' + data.destination + ' '+ data.status)
                    });
                }
        }

        function sizeReport(destination) {
            $.post('/size-report', { destination: destination }, function(data) {
                if (data.download_url) {
                    // Redirect the user to download the file
                    window.location.href = data.download_url;
                } else {
                    alert('Failed to generate the size report.');
                }
            }).fail(function() {
                alert('An error occurred while processing your request.');
            });
        }


    </script>
</body>
</html>
