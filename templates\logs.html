<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archive Copy Tool - Log Files</title>
    <link rel="stylesheet" href="../static/css/styles.css">
    <link rel="stylesheet" href="../static/css/explorer.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    {% include 'topbar.html' %}

    <div class="main-container">
        <div class="header-section">
            <div class="app-title">
                <i class="fas fa-file-alt"></i>
                <h1>Log Files</h1>
                <span class="subtitle">System Logs and Reports</span>
            </div>
        </div>

        <div class="logs-container">
            <div class="logs-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="fas fa-list"></i>
                        <span>Available Log Files</span>
                    </div>
                    <div class="panel-controls">
                        <button type="button" class="control-btn" onclick="location.reload()" title="Refresh">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button type="button" class="control-btn" onclick="window.history.back()" title="Back">
                            <i class="fas fa-arrow-left"></i>
                        </button>
                    </div>
                </div>

                <div class="logs-content">
                    {% if log_files %}
                        <div class="logs-grid">
                            {% for log_file in log_files %}
                                <div class="log-item" onclick="window.open('/{{dir}}/{{ log_file }}', '_blank')">
                                    <div class="log-icon">
                                        {% if 'critical' in log_file %}
                                            <i class="fas fa-exclamation-triangle" style="color: #f56565;"></i>
                                        {% elif 'info' in log_file %}
                                            <i class="fas fa-info-circle" style="color: #4299e1;"></i>
                                        {% elif 'size' in log_file %}
                                            <i class="fas fa-chart-bar" style="color: #48bb78;"></i>
                                        {% else %}
                                            <i class="fas fa-file-alt" style="color: #667eea;"></i>
                                        {% endif %}
                                    </div>
                                    <div class="log-info">
                                        <div class="log-name">{{ log_file }}</div>
                                        <div class="log-details">
                                            {% if 'critical' in log_file %}
                                                Critical System Log
                                            {% elif 'info' in log_file %}
                                                Information Log
                                            {% elif 'size' in log_file %}
                                                Size Report
                                            {% else %}
                                                System Log File
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="log-actions">
                                        <i class="fas fa-external-link-alt"></i>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-logs">
                            <i class="fas fa-folder-open"></i>
                            <span>No log files available</span>
                            <p>Log files will appear here when system activities are recorded.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <style>
        .logs-container {
            padding: 20px 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logs-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .logs-content {
            padding: 20px;
            min-height: 400px;
        }

        .logs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 15px;
        }

        .log-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .log-item:hover {
            background: #f8f9ff;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
        }

        .log-icon {
            font-size: 1.8rem;
            min-width: 30px;
            text-align: center;
        }

        .log-info {
            flex: 1;
            min-width: 0;
        }

        .log-name {
            font-weight: 600;
            color: #2d3748;
            font-size: 1rem;
            margin-bottom: 4px;
            word-break: break-all;
        }

        .log-details {
            font-size: 0.85rem;
            color: #718096;
        }

        .log-actions {
            color: #a0aec0;
            font-size: 1.2rem;
        }

        .empty-logs {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: #a0aec0;
            text-align: center;
        }

        .empty-logs i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-logs span {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .empty-logs p {
            font-size: 1rem;
            margin: 0;
            max-width: 400px;
        }

        @media (max-width: 768px) {
            .logs-container {
                padding: 15px 20px;
            }

            .logs-grid {
                grid-template-columns: 1fr;
            }

            .log-item {
                padding: 12px 15px;
            }
        }
    </style>
</body>
</html>
