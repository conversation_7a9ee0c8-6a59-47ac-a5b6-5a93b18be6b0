/* Professional Windows Explorer Style CSS */

/* Main Layout */
.main-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 0;
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.header-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.app-title i {
    font-size: 2.5rem;
    color: #667eea;
    text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.app-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    letter-spacing: -0.5px;
}

.subtitle {
    font-size: 1rem;
    color: #718096;
    font-weight: 400;
    margin-left: auto;
}

/* Explorer Container */
.explorer-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px 30px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Explorer Panel */
.explorer-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    min-height: 500px;
}

/* Panel Header */
.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    font-size: 1.1rem;
}

.panel-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Address Bar */
.address-bar {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 12px 20px;
}

.breadcrumb-container {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9rem;
    color: #495057;
}

.breadcrumb {
    background: white;
    padding: 6px 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    flex: 1;
    word-break: break-all;
}

/* Explorer Content */
.explorer-content {
    padding: 15px;
    min-height: 400px;
    position: relative;
}

.directory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
}

/* Directory Items */
.directory-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.directory-item:hover {
    background: #f8f9ff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.directory-item.go-up {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.directory-item.go-up:hover {
    background: #fff8e1;
    border-color: #ffb74d;
}

.item-icon {
    font-size: 1.5rem;
    color: #667eea;
    min-width: 24px;
    text-align: center;
}

.directory-item.go-up .item-icon {
    color: #f39c12;
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.item-details {
    font-size: 0.8rem;
    color: #718096;
    margin-top: 2px;
}

/* Empty Directory */
.empty-directory {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #a0aec0;
    font-size: 1.1rem;
    grid-column: 1 / -1;
}

.empty-directory i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #667eea;
    font-size: 1rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.loading-indicator i {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* Action Panel */
.action-panel {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 25px;
    margin: 0 30px 30px;
    max-width: 1340px;
    margin-left: auto;
    margin-right: auto;
}

.copy-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    justify-content: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.action-btn.danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.action-btn.danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Progress Section */
.progress-section {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 1.1rem;
}

.progress-percentage {
    font-weight: 700;
    color: #667eea;
    font-size: 1.2rem;
}

.progress-bar-container {
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 25px;
    background: #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    min-width: 0;
}

.progress-text {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-info {
    text-align: center;
    color: #718096;
    font-size: 0.9rem;
    font-style: italic;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    padding: 15px 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1000;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.notification-success { border-left-color: #48bb78; }
.notification-error { border-left-color: #f56565; }
.notification-warning { border-left-color: #ed8936; }
.notification-info { border-left-color: #4299e1; }

.notification i {
    font-size: 1.2rem;
}

.notification-success i { color: #48bb78; }
.notification-error i { color: #f56565; }
.notification-warning i { color: #ed8936; }
.notification-info i { color: #4299e1; }

.notification-close {
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: auto;
}

.notification-close:hover {
    background: #f7fafc;
    color: #4a5568;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .explorer-container {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px 20px;
    }
    
    .header-section {
        padding: 15px 20px;
    }
    
    .action-panel {
        margin: 0 20px 20px;
    }
}

@media (max-width: 768px) {
    .app-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .subtitle {
        margin-left: 0;
    }
    
    .directory-grid {
        grid-template-columns: 1fr;
    }
    
    .copy-controls {
        flex-direction: column;
    }
    
    .action-btn {
        min-width: auto;
    }
}
