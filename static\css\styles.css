/* Global Styles */
body, html {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Professional Navigation Bar */
.professional-navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    max-width: 1400px;
    margin: 0 auto;
    height: 70px;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
}

.navbar-brand i {
    font-size: 1.8rem;
    color: #667eea;
}

.navbar-menu {
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    color: #4a5568;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    position: relative;
}

.nav-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-1px);
}

.nav-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Dropdown Styles */
.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 4px;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a5568;
    font-weight: 500;
}

.user-info i {
    font-size: 1.5rem;
    color: #667eea;
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

/* Legacy styles for compatibility */
.container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
}

h1 {
    text-align: center;
    color: #2d3748;
    font-weight: 700;
    margin-bottom: 30px;
}

label {
    font-weight: 600;
    color: #4a5568;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-container {
        padding: 0 15px;
        height: auto;
        flex-direction: column;
        gap: 15px;
        padding-top: 15px;
        padding-bottom: 15px;
    }

    .navbar-menu {
        flex-wrap: wrap;
        justify-content: center;
    }

    .navbar-user {
        order: -1;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: transparent;
    }

    .nav-dropdown:hover .dropdown-menu {
        display: block;
    }
}