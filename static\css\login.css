/* Professional Login Page Styles */

.login-body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    margin-bottom: 40px;
}

.logo-container {
    margin-bottom: 20px;
}

.logo-icon {
    font-size: 4rem;
    color: #667eea;
    text-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.login-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 10px 0;
    letter-spacing: -0.5px;
}

.login-subtitle {
    font-size: 1rem;
    color: #718096;
    margin: 0;
    font-weight: 400;
}

.login-form-container {
    margin-bottom: 30px;
}

.flash-messages {
    margin-bottom: 25px;
}

.flash-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    font-weight: 500;
}

.flash-error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.flash-success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
}

.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.form-input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f7fafc;
    box-sizing: border-box;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #718096;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #4a5568;
    background-color: rgba(0, 0, 0, 0.05);
}

.login-button {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-footer {
    border-top: 1px solid #e2e8f0;
    padding-top: 25px;
    margin-top: 30px;
}

.security-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.copyright {
    color: #a0aec0;
    font-size: 0.8rem;
}

.copyright p {
    margin: 0;
}

.background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: 1;
}

/* Responsive Design */
@media (max-width: 480px) {
    .login-container {
        padding: 15px;
    }
    
    .login-card {
        padding: 30px 25px;
    }
    
    .login-title {
        font-size: 1.8rem;
    }
    
    .logo-icon {
        font-size: 3rem;
    }
}

/* Animation for form elements */
.form-group {
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.login-button { 
    animation: slideInUp 0.6s ease forwards;
    animation-delay: 0.3s;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
