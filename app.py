import sys
from flask import Flask, Response, render_template, request, jsonify, send_file, redirect, url_for
import os
import shutil
import threading
import hashlib
import logging
from datetime import datetime, timedelta
import paramiko


DEBUG = False
USERNAME = 'archives'
PASSWORD = 'donotcopyme'

CATDV_HOST = ''
SSH_USER = ''
SSH_KEY_FILE = '/opt/dwara/.ssh/id_rsa'

# Create a custom formatter
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Create a log directory if it doesn't exist
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# Configure logging
def get_log_file(prefix):
    now = datetime.now()
    month_year = now.strftime("%Y-%m")
    log_file = os.path.join(log_dir, f"{prefix}_{month_year}.txt")
    return log_file

# log_file = get_log_file('info')
# file_handler = logging.FileHandler(log_file)
# file_handler.setFormatter(formatter)
# file_handler.setLevel(logging.INFO)

logger = logging.getLogger()
# logger.setLevel(logging.INFO)
# logger.addHandler(file_handler)

critical_log_file = get_log_file('critical')
critical_handler = logging.FileHandler(critical_log_file)
critical_handler.setFormatter(formatter)
critical_handler.setLevel(logging.CRITICAL)
logger.addHandler(critical_handler)

# Decorator for basic authentication
def require_auth(func):
    def wrapper(*args, **kwargs):
        auth = request.authorization
        if not auth or not (auth.username == USERNAME and auth.password == PASSWORD):
            return Response('Unauthorized', 401, {'WWW-Authenticate': 'Basic realm="Login Required"'})
        return func(*args, **kwargs)
    return wrapper

app = Flask(__name__)

progress = 0
stop_copy = False
copy_thread = None
copying = False

def calculate_checksum(file_path):
    sha256 = hashlib.sha256()
    with open(file_path, 'rb') as f:
        while True:
            buffer = f.read(1024 * 1024)
            if not buffer:
                break
            sha256.update(buffer)
    return sha256.hexdigest()

def copy_file(src, dst):
    global progress, stop_copy, copying
    total_size = os.path.getsize(src)
    copied_size = 0

    with open(src, 'rb') as fsrc, open(dst, 'wb') as fdst:
        while True:
            if stop_copy:
                copying = False
                return
            buffer = fsrc.read(1024 * 1024)
            if not buffer:
                break
            fdst.write(buffer)
            copied_size += len(buffer)
            progress = (copied_size / total_size) * 100

    # Verify checksum
    # if not stop_copy:
    #     src_checksum = calculate_checksum(src)
    #     dst_checksum = calculate_checksum(dst)
    #     copying = False
    #     return src_checksum == dst_checksum
    copying = False
    logger.critical(f'finish copying {src} to {dst}')
    return False

def copy_directory(src, dst):
    global progress, stop_copy, copying
    total_size = sum(os.path.getsize(os.path.join(root, file)) for root, _, files in os.walk(src) for file in files)
    copied_size = 0

    if not os.path.exists(dst):
        os.makedirs(dst)

    for root, dirs, files in os.walk(src):
        for directory in dirs:
            if stop_copy:
                copying = False
                return
            dir_path = os.path.join(dst, os.path.relpath(os.path.join(root, directory), src))
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)

        for file in files:
            if stop_copy:
                copying = False
                return
            file_src_path = os.path.join(root, file)
            file_dst_path = os.path.join(dst, os.path.relpath(file_src_path, src))

            with open(file_src_path, 'rb') as fsrc, open(file_dst_path, 'wb') as fdst:
                while True:
                    if stop_copy:
                        copying = False
                        return
                    buffer = fsrc.read(1024 * 1024)
                    if not buffer:
                        break
                    fdst.write(buffer)
                    copied_size += len(buffer)
                    progress = (copied_size / total_size) * 100
                    logger.critical(f'copying {progress} {file_src_path}')

            # Verify checksum
            # if not stop_copy:
            #     src_checksum = calculate_checksum(file_src_path)
            #     dst_checksum = calculate_checksum(file_dst_path)
            #     if src_checksum != dst_checksum:
            #         copying = False
            #         return False
    copying = False
    logger.critical(f'finish copying {src} to {dst}')
    return not stop_copy

@app.route('/')
@require_auth
def index():
    return render_template('index.html')

@app.route('/list_directories', methods=['POST'])
def list_directories():
    directory = request.form.get('directory', '')
    try:
        subdirectories = sorted([d for d in os.listdir(directory) if os.path.isdir(os.path.join(directory, d))])
        return jsonify({'subdirectories': subdirectories})
    except FileNotFoundError:
        return jsonify({'error': 'Directory not found'})

@app.route('/copy', methods=['POST'])
def copy():
    global progress, stop_copy, copy_thread, copying
    src = request.form['src']
    dst = request.form['dst']

    # Get the folder name from the src path
    folder_name = os.path.basename(src.rstrip('/'))

    # Create the new destination path
    dst = os.path.join(dst, folder_name)

    logger.critical(f'start copying {src} to {dst}')

    progress = 0
    stop_copy = False
    copying = True
    
    if os.path.isdir(src):
        copy_thread = threading.Thread(target=copy_directory, args=(src, dst))
    else:
        copy_thread = threading.Thread(target=copy_file, args=(src, dst))

    copy_thread.start()

    return jsonify({'status': 'copying started'})

@app.route('/progress', methods=['GET'])
def get_progress():
    global progress, copying
    return jsonify({'progress': progress, 'copying': copying})

@app.route('/stop', methods=['POST'])
def stop():
    global stop_copy, copy_thread
    stop_copy = True
    if copy_thread:
        copy_thread.join()  # Wait for the thread to finish
    return jsonify({'status': 'copying stopped'})

@app.route('/logs/')
def list_logs():
    log_files = os.listdir(log_dir)
    return render_template('logs.html', dir='logs', log_files=log_files)

@app.route('/logs/<filename>')
def view_log(filename):
    log_path = os.path.join(log_dir, filename)
    if not os.path.isfile(log_path):
        info={'title': 'Error', 'message': 'File not found!'}
        return render_template('message.html', info=info)
    return send_file(log_path)

@app.route('/clean-up', methods=['POST'])
def clean():
    clean_up_destination = request.form.get('clean_up_destination', '')
    #validate destination
    if 'delete' not in clean_up_destination.lower():
        return jsonify({'destination':clean_up_destination, 'status': 'forbidden'})
    #clean_up_destination = '/mnt/bru/RESTORED/archives/mapping-restore/TO_Check/ZTO Delete'
    #folder_path = '/Users/<USER>/Downloads/test clean up'
    logger.critical(f'start cleaning up {clean_up_destination}')
    # Check if the folder exists
    if os.path.exists(clean_up_destination):
        # Iterate over all the files and directories in the folder
        for filename in os.listdir(clean_up_destination):
            file_path = os.path.join(clean_up_destination, filename)
            try:
                # If it's a file, remove it
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)
                # If it's a directory, remove it and all its contents
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
            except Exception as e:
                logger.critical(f'Failed to delete {file_path}. Reason: {e}')
                return jsonify({'destination':clean_up_destination, 'status': 'failed'})
    else:
        logger.critical(f'The folder "{clean_up_destination}" does not exist.')
        return jsonify({'destination':clean_up_destination, 'status': 'failed'})
    logger.critical(f'sucessfully cleaning up {clean_up_destination}')
    return jsonify({'destination':clean_up_destination, 'status': 'successful'})

@app.route('/size-report', methods=['POST'])
def size_report():
    destination = request.form.get('destination', '')
    
    now = datetime.now()
    time = now.strftime("%Y-%m-%d-%H-%M-%S")
    file_name = f"{destination}_size_{time}.txt"
    log_file = os.path.join(log_dir, file_name)
    
    # Ensure the logs folder exists
    os.makedirs(log_dir, exist_ok=True)

    # Run the command and save the output to a file
    if destination == 'ingest':
        command = 'du -h -d 2 /data'
        with open(log_file, 'w') as f:
            process = os.popen(command)
            f.write(process.read())
            process.close()
    elif destination == 'bru':
        command = 'du -h -d 2 /mnt/bru/RESTORED/archives'
        with open(log_file, 'w') as f:
            process = os.popen(command)
            f.write(process.read())
            process.close()
    elif destination == 'catdv':
        try:
            # Set up the SSH client
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(CATDV_HOST, username=SSH_USER, key_filename=SSH_KEY_FILE)

            # Command to run remotely
            command = 'du -h -d 2 /data'

            # Execute the command
            stdin, stdout, stderr = ssh.exec_command(command)
            output = stdout.read().decode()
            error = stderr.read().decode()

            # Save the output to a log file
            with open(log_file, 'w') as f:
                f.write(output)

            ssh.close()

            if error:
                return jsonify({"error": error}), 500
        except Exception as e:
            return jsonify({"error": str(e)}), 500
        
    print(f'destination {destination}, command {command}, file_name {file_name}')        
            
    download_url = url_for('view_log', filename=file_name)
    return jsonify({"download_url": download_url})
    
if __name__ == '__main__':
    if DEBUG:
        app.run(debug=True)
    else:
        port = int(sys.argv[1]) if len(sys.argv) > 1 else 5000
        app.run(host='0.0.0.0', port=port)
